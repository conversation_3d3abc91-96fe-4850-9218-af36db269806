import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
import torchvision.models as models
from PIL import Image
import pandas as pd
import numpy as np
import os
import cv2
from sklearn.metrics import accuracy_score, classification_report, roc_auc_score
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# Disease classes
DISEASES = [
    'Atelectasis', 'Cardiomegaly', 'Consolidation', 'Edema', 'Effusion',
    'Emphysema', 'Fibrosis', 'Infiltration', 'Mass', 'Nodule',
    'Pleural_Thickening', 'Pneumonia', 'Pneumothorax'
]

class MedicalImageDataset(Dataset):
    def __init__(self, image_paths, labels, transform=None):
        self.image_paths = image_paths
        self.labels = labels
        self.transform = transform
    
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        image_path = self.image_paths[idx]
        try:
            image = Image.open(image_path).convert('RGB')
        except:
            # Create dummy image if file can't be opened
            image = Image.new('RGB', (512, 512), color='black')
        
        if self.transform:
            image = self.transform(image)
        
        label = torch.FloatTensor(self.labels[idx])
        return image, label

class AdvancedMedicalClassifier(nn.Module):
    def __init__(self, num_classes=13, pretrained=True):
        super(AdvancedMedicalClassifier, self).__init__()

        # Use EfficientNet-B3 for good balance of accuracy and speed
        self.backbone = models.efficientnet_b3(pretrained=pretrained)
        num_features = self.backbone.classifier[1].in_features
        self.backbone.classifier = nn.Identity()

        # Multi-scale feature extraction
        self.global_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)

        # Advanced attention mechanism
        self.attention = nn.MultiheadAttention(embed_dim=num_features, num_heads=16, dropout=0.1)
        self.layer_norm1 = nn.LayerNorm(num_features)
        self.layer_norm2 = nn.LayerNorm(num_features)

        # Feature enhancement
        self.feature_enhancer = nn.Sequential(
            nn.Linear(num_features, num_features),
            nn.ReLU(),
            nn.Dropout(0.1)
        )

        # Advanced classifier with residual connections
        self.classifier = nn.Sequential(
            nn.Dropout(0.4),
            nn.Linear(num_features, 1024),
            nn.ReLU(),
            nn.BatchNorm1d(1024),
            nn.Dropout(0.3),
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.BatchNorm1d(512),
            nn.Dropout(0.2),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.BatchNorm1d(256),
            nn.Dropout(0.1),
            nn.Linear(256, num_classes)
        )

        # Initialize weights
        self._initialize_weights()

    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        # Extract features from backbone
        features = self.backbone(x)

        # Apply attention mechanism
        features_unsqueezed = features.unsqueeze(0)
        attended_features, attention_weights = self.attention(
            features_unsqueezed, features_unsqueezed, features_unsqueezed
        )
        attended_features = attended_features.squeeze(0)

        # Layer normalization and residual connection
        features = self.layer_norm1(attended_features + features)

        # Feature enhancement
        enhanced_features = self.feature_enhancer(features)
        features = self.layer_norm2(enhanced_features + features)

        # Classification (no sigmoid for BCEWithLogitsLoss)
        output = self.classifier(features)
        return output

def get_transforms():
    # Advanced augmentation for medical images
    train_transform = transforms.Compose([
        transforms.Resize((384, 384)),  # Optimized resolution for CPU training
        transforms.RandomRotation(15),
        transforms.RandomHorizontalFlip(0.5),
        transforms.RandomVerticalFlip(0.1),
        transforms.ColorJitter(brightness=0.3, contrast=0.3, saturation=0.1),
        transforms.RandomAffine(degrees=0, translate=(0.1, 0.1), scale=(0.9, 1.1)),
        transforms.RandomPerspective(distortion_scale=0.1, p=0.3),
        transforms.GaussianBlur(kernel_size=3, sigma=(0.1, 2.0)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        transforms.RandomErasing(p=0.1, scale=(0.02, 0.1))
    ])

    val_transform = transforms.Compose([
        transforms.Resize((384, 384)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    return train_transform, val_transform

def load_data():
    print("Loading NIH Chest X-ray dataset...")

    # Load the CSV file with labels
    try:
        df = pd.read_csv('Data_Entry_2017.csv')
        print(f"Loaded CSV with {len(df)} entries")
    except:
        print("Could not load Data_Entry_2017.csv, using image discovery...")
        return load_data_fallback()

    # Get all image paths from the dataset folders
    image_paths = []
    labels = []

    # Map disease names to indices
    disease_to_idx = {disease: idx for idx, disease in enumerate(DISEASES)}

    for _, row in df.iterrows():
        image_name = row['Image Index']
        finding_labels = row['Finding Labels']

        # Find the image file in the folders
        image_path = None
        for i in range(1, 13):  # images_001 to images_012
            folder_path = f'images_{i:03d}/images/{image_name}'
            if os.path.exists(folder_path):
                image_path = folder_path
                break

        if image_path and os.path.exists(image_path):
            image_paths.append(image_path)

            # Create multi-label vector
            label_vector = np.zeros(len(DISEASES), dtype=np.float32)

            # Parse finding labels
            if finding_labels != 'No Finding':
                findings = [f.strip() for f in finding_labels.split('|')]
                for finding in findings:
                    if finding in disease_to_idx:
                        label_vector[disease_to_idx[finding]] = 1.0

            labels.append(label_vector)

    print(f"Found {len(image_paths)} valid images with labels")
    return image_paths, labels

def load_data_fallback():
    print("Using fallback image discovery...")
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    image_paths = []

    # Look specifically in images_* folders
    for i in range(1, 13):
        folder_path = f'images_{i:03d}/images'
        if os.path.exists(folder_path):
            for file in os.listdir(folder_path):
                if any(file.lower().endswith(ext) for ext in image_extensions):
                    image_paths.append(os.path.join(folder_path, file))

    print(f"Found {len(image_paths)} images")

    # Generate synthetic labels for demo
    labels = []
    for _ in image_paths:
        label = np.random.randint(0, 2, size=len(DISEASES)).astype(np.float32)
        labels.append(label)

    return image_paths, labels

def train_model():
    print("Starting Medical AI Training...")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    image_paths, labels = load_data()
    
    if len(image_paths) == 0:
        print("No images found! Creating sample data...")
        # Create sample data for testing
        os.makedirs('sample_images', exist_ok=True)
        for i in range(100):
            img = Image.new('RGB', (512, 512), color=(np.random.randint(0, 255), np.random.randint(0, 255), np.random.randint(0, 255)))
            img.save(f'sample_images/sample_{i}.jpg')
        image_paths, labels = load_data()
    
    train_paths, val_paths, train_labels, val_labels = train_test_split(
        image_paths, labels, test_size=0.2, random_state=42
    )
    
    train_transform, val_transform = get_transforms()
    
    train_dataset = MedicalImageDataset(train_paths, train_labels, train_transform)
    val_dataset = MedicalImageDataset(val_paths, val_labels, val_transform)
    
    # Optimized batch sizes and data loading
    batch_size = 8 if torch.cuda.is_available() else 4
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True,
                             num_workers=2, pin_memory=True, drop_last=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False,
                           num_workers=2, pin_memory=True)

    model = AdvancedMedicalClassifier(num_classes=len(DISEASES))
    model = model.to(device)

    # Advanced loss function with class weighting
    pos_weight = torch.ones(len(DISEASES)) * 2.0  # Weight positive cases more
    criterion = nn.BCEWithLogitsLoss(pos_weight=pos_weight.to(device))

    # Advanced optimizer with better parameters
    optimizer = optim.AdamW(model.parameters(), lr=2e-4, weight_decay=1e-4,
                           betas=(0.9, 0.999), eps=1e-8)

    # Cosine annealing with warm restarts
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=10, T_mult=2, eta_min=1e-6
    )

    # Mixed precision training for faster training
    scaler = torch.cuda.amp.GradScaler() if torch.cuda.is_available() else None

    num_epochs = 25  # Reasonable epochs for initial training
    best_val_loss = float('inf')
    
    print(f"Training for {num_epochs} epochs...")
    
    for epoch in range(num_epochs):
        model.train()
        train_loss = 0.0
        
        for images, labels_batch in tqdm(train_loader, desc=f'Epoch {epoch+1}'):
            images, labels_batch = images.to(device), labels_batch.to(device)

            optimizer.zero_grad()

            # Mixed precision training
            if scaler is not None:
                with torch.cuda.amp.autocast():
                    outputs = model(images)
                    # Remove sigmoid from model output for BCEWithLogitsLoss
                    loss = criterion(outputs, labels_batch)

                scaler.scale(loss).backward()
                scaler.step(optimizer)
                scaler.update()
            else:
                outputs = model(images)
                loss = criterion(outputs, labels_batch)
                loss.backward()
                optimizer.step()

            train_loss += loss.item()
        
        model.eval()
        val_loss = 0.0
        all_preds = []
        all_labels = []
        
        with torch.no_grad():
            for images, labels_batch in val_loader:
                images, labels_batch = images.to(device), labels_batch.to(device)

                if scaler is not None:
                    with torch.cuda.amp.autocast():
                        outputs = model(images)
                        loss = criterion(outputs, labels_batch)
                else:
                    outputs = model(images)
                    loss = criterion(outputs, labels_batch)

                val_loss += loss.item()

                # Apply sigmoid for predictions since we removed it from model
                probs = torch.sigmoid(outputs)
                preds = (probs > 0.5).float()
                all_preds.append(preds.cpu().numpy())
                all_labels.append(labels_batch.cpu().numpy())
        
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        
        if len(all_preds) > 0:
            all_preds = np.vstack(all_preds)
            all_labels = np.vstack(all_labels)
            accuracy = accuracy_score(all_labels.flatten(), all_preds.flatten())
        else:
            accuracy = 0.0
        
        print(f'Epoch {epoch+1}: Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}, Accuracy: {accuracy:.4f}')
        
        scheduler.step(val_loss)
        
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            torch.save(model.state_dict(), 'best_medical_model.pth')
            print(f'Best model saved! Val Loss: {val_loss:.4f}')
    
    print("Training completed!")
    return model

def test_model():
    print("Testing the trained model...")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    model = AdvancedMedicalClassifier(num_classes=len(DISEASES))
    
    try:
        model.load_state_dict(torch.load('best_medical_model.pth', map_location=device))
        print("Loaded trained model successfully!")
    except:
        print("No trained model found. Training first...")
        model = train_model()
    
    model = model.to(device)
    model.eval()
    
    # Test with sample prediction
    sample_input = torch.randn(1, 3, 384, 384).to(device)

    with torch.no_grad():
        logits = model(sample_input)
        prediction = torch.sigmoid(logits).cpu().numpy()[0]
    
    print("\nSample Prediction Results:")
    print("-" * 50)
    for i, disease in enumerate(DISEASES):
        prob = prediction[i]
        positive = prob > 0.5
        uncertainty = abs(0.5 - prob)
        print(f"{disease}: Probability={prob:.3f}, Uncertainty={uncertainty:.3f}, Positive={positive}")
    
    return model

if __name__ == "__main__":
    # Train the model
    trained_model = train_model()
    
    # Test the model
    test_model()
    
    print("\nMedical AI Model Training and Testing Complete!")
    print("Model saved as 'best_medical_model.pth'")
