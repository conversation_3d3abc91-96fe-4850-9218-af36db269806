import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
import torchvision.models as models
from PIL import Image
import pandas as pd
import numpy as np
import os
import cv2
from sklearn.metrics import accuracy_score, classification_report, roc_auc_score
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# Disease classes
DISEASES = [
    'Atelectasis', 'Cardiomegaly', 'Consolidation', 'Edema', 'Effusion',
    'Emphysema', 'Fibrosis', 'Infiltration', 'Mass', 'Nodule',
    'Pleural_Thickening', 'Pneumonia', 'Pneumothorax'
]

class MedicalImageDataset(Dataset):
    def __init__(self, image_paths, labels, transform=None):
        self.image_paths = image_paths
        self.labels = labels
        self.transform = transform
    
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        image_path = self.image_paths[idx]
        image = Image.open(image_path).convert('RGB')
        
        if self.transform:
            image = self.transform(image)
        
        label = torch.FloatTensor(self.labels[idx])
        return image, label

class MultiModalMedicalClassifier(nn.Module):
    def __init__(self, num_classes=13, pretrained=True):
        super(MultiModalMedicalClassifier, self).__init__()
        
        # Use EfficientNet-B4 as backbone (state-of-the-art for medical imaging)
        self.backbone = models.efficientnet_b4(pretrained=pretrained)
        
        # Replace classifier with custom head
        num_features = self.backbone.classifier[1].in_features
        self.backbone.classifier = nn.Identity()
        
        # Advanced classifier head with attention mechanism
        self.attention = nn.MultiheadAttention(embed_dim=num_features, num_heads=8)
        self.layer_norm = nn.LayerNorm(num_features)
        
        self.classifier = nn.Sequential(
            nn.Dropout(0.3),
            nn.Linear(num_features, 512),
            nn.ReLU(),
            nn.BatchNorm1d(512),
            nn.Dropout(0.2),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.BatchNorm1d(256),
            nn.Dropout(0.1),
            nn.Linear(256, num_classes)
        )
        
        # Initialize weights
        self._initialize_weights()
    
    def _initialize_weights(self):
        for m in self.classifier.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        # Extract features
        features = self.backbone(x)
        
        # Apply attention mechanism
        features_unsqueezed = features.unsqueeze(0)
        attended_features, _ = self.attention(features_unsqueezed, features_unsqueezed, features_unsqueezed)
        attended_features = attended_features.squeeze(0)
        
        # Layer normalization and residual connection
        features = self.layer_norm(attended_features + features)
        
        # Classification
        output = self.classifier(features)
        return torch.sigmoid(output)

def get_transforms():
    train_transform = transforms.Compose([
        transforms.Resize((512, 512)),
        transforms.RandomRotation(10),
        transforms.RandomHorizontalFlip(0.5),
        transforms.RandomVerticalFlip(0.1),
        transforms.ColorJitter(brightness=0.2, contrast=0.2),
        transforms.RandomAffine(degrees=0, translate=(0.1, 0.1)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    val_transform = transforms.Compose([
        transforms.Resize((512, 512)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    return train_transform, val_transform

def load_data():
    """Load and prepare the dataset"""
    print("Loading dataset...")
    
    # Find all image files in the directory
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    image_paths = []
    
    for root, dirs, files in os.walk('.'):
        for file in files:
            if any(file.lower().endswith(ext) for ext in image_extensions):
                image_paths.append(os.path.join(root, file))
    
    print(f"Found {len(image_paths)} images")
    
    # For demonstration, create synthetic labels
    # In real scenario, you would load from CSV or annotation files
    labels = []
    for _ in image_paths:
        # Generate random binary labels for each disease
        label = np.random.randint(0, 2, size=len(DISEASES)).astype(np.float32)
        labels.append(label)
    
    return image_paths, labels

def calculate_metrics(y_true, y_pred, y_prob):
    """Calculate comprehensive metrics"""
    metrics = {}
    
    # Overall accuracy
    metrics['accuracy'] = accuracy_score(y_true.flatten(), y_pred.flatten())
    
    # Per-class metrics
    for i, disease in enumerate(DISEASES):
        y_true_class = y_true[:, i]
        y_pred_class = y_pred[:, i]
        y_prob_class = y_prob[:, i]
        
        accuracy = accuracy_score(y_true_class, y_pred_class)
        try:
            auc = roc_auc_score(y_true_class, y_prob_class)
        except:
            auc = 0.5
        
        metrics[f'{disease}_accuracy'] = accuracy
        metrics[f'{disease}_auc'] = auc
    
    return metrics

def train_model():
    """Main training function"""
    print("Starting Medical Multimodal Classifier Training...")
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Load data
    image_paths, labels = load_data()
    
    if len(image_paths) == 0:
        print("No images found! Please ensure you have medical images in the directory.")
        return
    
    # Split data
    train_paths, val_paths, train_labels, val_labels = train_test_split(
        image_paths, labels, test_size=0.2, random_state=42
    )
    
    # Get transforms
    train_transform, val_transform = get_transforms()
    
    # Create datasets
    train_dataset = MedicalImageDataset(train_paths, train_labels, train_transform)
    val_dataset = MedicalImageDataset(val_paths, val_labels, val_transform)
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=8, shuffle=False, num_workers=2)
    
    # Initialize model
    model = MultiModalMedicalClassifier(num_classes=len(DISEASES))
    model = model.to(device)
    
    # Loss function and optimizer
    criterion = nn.BCELoss()
    optimizer = optim.AdamW(model.parameters(), lr=1e-4, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=5, factor=0.5)
    
    # Training loop
    num_epochs = 50
    best_val_loss = float('inf')
    train_losses = []
    val_losses = []
    
    print(f"Training for {num_epochs} epochs...")
    
    for epoch in range(num_epochs):
        # Training phase
        model.train()
        train_loss = 0.0
        
        for batch_idx, (images, labels) in enumerate(tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs}')):
            images, labels = images.to(device), labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        all_preds = []
        all_labels = []
        all_probs = []
        
        with torch.no_grad():
            for images, labels in val_loader:
                images, labels = images.to(device), labels.to(device)
                outputs = model(images)
                loss = criterion(outputs, labels)
                val_loss += loss.item()
                
                # Store predictions for metrics
                preds = (outputs > 0.5).float()
                all_preds.append(preds.cpu().numpy())
                all_labels.append(labels.cpu().numpy())
                all_probs.append(outputs.cpu().numpy())
        
        # Calculate metrics
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        
        # Calculate validation metrics
        all_preds = np.vstack(all_preds)
        all_labels = np.vstack(all_labels)
        all_probs = np.vstack(all_probs)
        
        metrics = calculate_metrics(all_labels, all_preds, all_probs)
        
        print(f'Epoch {epoch+1}/{num_epochs}:')
        print(f'Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}')
        print(f'Val Accuracy: {metrics["accuracy"]:.4f}')
        
        # Learning rate scheduling
        scheduler.step(val_loss)
        
        # Save best model
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            torch.save(model.state_dict(), 'best_medical_model.pth')
            print(f'New best model saved with validation loss: {val_loss:.4f}')
        
        print('-' * 50)
    
    # Plot training curves
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 2, 1)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Validation Loss')
    plt.title('Training and Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    
    plt.subplot(1, 2, 2)
    # Plot per-disease accuracy
    disease_accuracies = [metrics[f'{disease}_accuracy'] for disease in DISEASES]
    plt.bar(range(len(DISEASES)), disease_accuracies)
    plt.title('Per-Disease Accuracy')
    plt.xlabel('Disease')
    plt.ylabel('Accuracy')
    plt.xticks(range(len(DISEASES)), DISEASES, rotation=45)
    
    plt.tight_layout()
    plt.savefig('training_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Training completed!")
    print(f"Best validation loss: {best_val_loss:.4f}")
    print(f"Final validation accuracy: {metrics['accuracy']:.4f}")
    
    return model

if __name__ == "__main__":
    model = train_model()
