import torch
import pandas as pd
import os

print("Testing environment...")
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA device: {torch.cuda.get_device_name()}")

print("\nChecking dataset...")
try:
    df = pd.read_csv('Data_Entry_2017.csv')
    print(f"CSV loaded successfully: {len(df)} entries")
    print(f"Columns: {list(df.columns)}")
    print(f"Sample finding labels: {df['Finding Labels'].head()}")
except Exception as e:
    print(f"Error loading CSV: {e}")

print("\nChecking image folders...")
image_count = 0
for i in range(1, 13):
    folder_path = f'images_{i:03d}/images'
    if os.path.exists(folder_path):
        files = [f for f in os.listdir(folder_path) if f.lower().endswith(('.jpg', '.png', '.jpeg'))]
        print(f"{folder_path}: {len(files)} images")
        image_count += len(files)
    else:
        print(f"{folder_path}: Not found")

print(f"\nTotal images found: {image_count}")
