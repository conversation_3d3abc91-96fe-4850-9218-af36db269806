import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
import torchvision.models as models
from PIL import Image
import pandas as pd
import numpy as np
import os
from sklearn.metrics import accuracy_score, classification_report, roc_auc_score
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# Disease classes
DISEASES = [
    'Atelectasis', 'Cardiomegaly', 'Consolidation', 'Edema', 'Effusion',
    'Emphysema', 'Fibrosis', 'Infiltration', 'Mass', 'Nodule',
    'Pleural_Thickening', 'Pneumonia', 'Pneumothorax'
]

class MedicalImageDataset(Dataset):
    def __init__(self, image_paths, labels, transform=None):
        self.image_paths = image_paths
        self.labels = labels
        self.transform = transform
    
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        image_path = self.image_paths[idx]
        try:
            image = Image.open(image_path).convert('RGB')
        except:
            # Create dummy image if file can't be opened
            image = Image.new('RGB', (224, 224), color='black')
        
        if self.transform:
            image = self.transform(image)
        
        label = torch.FloatTensor(self.labels[idx])
        return image, label

class FastMedicalClassifier(nn.Module):
    def __init__(self, num_classes=13, pretrained=True):
        super(FastMedicalClassifier, self).__init__()
        
        # Use ResNet50 for faster training
        self.backbone = models.resnet50(pretrained=pretrained)
        num_features = self.backbone.fc.in_features
        self.backbone.fc = nn.Identity()
        
        # Efficient classifier
        self.classifier = nn.Sequential(
            nn.Dropout(0.3),
            nn.Linear(num_features, 512),
            nn.ReLU(),
            nn.BatchNorm1d(512),
            nn.Dropout(0.2),
            nn.Linear(512, num_classes)
        )
    
    def forward(self, x):
        features = self.backbone(x)
        output = self.classifier(features)
        return output

def get_transforms():
    train_transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.RandomHorizontalFlip(0.5),
        transforms.RandomRotation(10),
        transforms.ColorJitter(brightness=0.2, contrast=0.2),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    val_transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    return train_transform, val_transform

def load_data_fast(max_samples=5000):
    print(f"Loading NIH Chest X-ray dataset (max {max_samples} samples)...")
    
    try:
        df = pd.read_csv('Data_Entry_2017.csv')
        print(f"Loaded CSV with {len(df)} entries")
    except:
        print("Could not load Data_Entry_2017.csv")
        return [], []
    
    # Sample a subset for faster training
    df_sample = df.sample(n=min(max_samples, len(df)), random_state=42)
    
    image_paths = []
    labels = []
    
    # Map disease names to indices
    disease_to_idx = {disease: idx for idx, disease in enumerate(DISEASES)}
    
    print("Processing images...")
    for _, row in tqdm(df_sample.iterrows(), total=len(df_sample)):
        image_name = row['Image Index']
        finding_labels = row['Finding Labels']
        
        # Find the image file in the folders
        image_path = None
        for i in range(1, 13):
            folder_path = f'images_{i:03d}/images/{image_name}'
            if os.path.exists(folder_path):
                image_path = folder_path
                break
        
        if image_path and os.path.exists(image_path):
            image_paths.append(image_path)
            
            # Create multi-label vector
            label_vector = np.zeros(len(DISEASES), dtype=np.float32)
            
            # Parse finding labels
            if finding_labels != 'No Finding':
                findings = [f.strip() for f in finding_labels.split('|')]
                for finding in findings:
                    if finding in disease_to_idx:
                        label_vector[disease_to_idx[finding]] = 1.0
            
            labels.append(label_vector)
    
    print(f"Found {len(image_paths)} valid images with labels")
    return image_paths, labels

def train_model():
    print("Starting Fast Medical AI Training...")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Load subset of data for faster training
    image_paths, labels = load_data_fast(max_samples=2000)
    
    if len(image_paths) == 0:
        print("No images found!")
        return None
    
    # Split data
    train_paths, val_paths, train_labels, val_labels = train_test_split(
        image_paths, labels, test_size=0.2, random_state=42
    )
    
    print(f"Training samples: {len(train_paths)}")
    print(f"Validation samples: {len(val_paths)}")
    
    # Get transforms
    train_transform, val_transform = get_transforms()
    
    # Create datasets
    train_dataset = MedicalImageDataset(train_paths, train_labels, train_transform)
    val_dataset = MedicalImageDataset(val_paths, val_labels, val_transform)
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=16, shuffle=False, num_workers=0)
    
    # Initialize model
    model = FastMedicalClassifier(num_classes=len(DISEASES))
    model = model.to(device)
    
    # Loss function and optimizer
    criterion = nn.BCEWithLogitsLoss()
    optimizer = optim.AdamW(model.parameters(), lr=1e-3, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=3, factor=0.5)
    
    num_epochs = 15
    best_val_loss = float('inf')
    train_losses = []
    val_losses = []
    val_accuracies = []
    
    print(f"Training for {num_epochs} epochs...")
    
    for epoch in range(num_epochs):
        # Training phase
        model.train()
        train_loss = 0.0
        
        for images, labels_batch in tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs}'):
            images, labels_batch = images.to(device), labels_batch.to(device)
            
            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, labels_batch)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        all_preds = []
        all_labels = []
        
        with torch.no_grad():
            for images, labels_batch in val_loader:
                images, labels_batch = images.to(device), labels_batch.to(device)
                outputs = model(images)
                loss = criterion(outputs, labels_batch)
                val_loss += loss.item()
                
                # Apply sigmoid for predictions
                probs = torch.sigmoid(outputs)
                preds = (probs > 0.5).float()
                all_preds.append(preds.cpu().numpy())
                all_labels.append(labels_batch.cpu().numpy())
        
        # Calculate metrics
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        
        if len(all_preds) > 0:
            all_preds = np.vstack(all_preds)
            all_labels = np.vstack(all_labels)
            accuracy = accuracy_score(all_labels.flatten(), all_preds.flatten())
        else:
            accuracy = 0.0
        
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        val_accuracies.append(accuracy)
        
        print(f'Epoch {epoch+1}: Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}, Accuracy: {accuracy:.4f}')
        
        # Learning rate scheduling
        scheduler.step(val_loss)
        
        # Save best model
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            torch.save(model.state_dict(), 'best_fast_medical_model.pth')
            print(f'Best model saved! Val Loss: {val_loss:.4f}, Accuracy: {accuracy:.4f}')
    
    # Plot training curves
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Validation Loss')
    plt.title('Training and Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    
    plt.subplot(1, 3, 2)
    plt.plot(val_accuracies, label='Validation Accuracy')
    plt.title('Validation Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.legend()
    
    plt.subplot(1, 3, 3)
    # Calculate per-disease accuracy for final epoch
    disease_accuracies = []
    for i in range(len(DISEASES)):
        if len(all_labels) > 0:
            disease_acc = accuracy_score(all_labels[:, i], all_preds[:, i])
            disease_accuracies.append(disease_acc)
        else:
            disease_accuracies.append(0.0)
    
    plt.bar(range(len(DISEASES)), disease_accuracies)
    plt.title('Per-Disease Accuracy')
    plt.xlabel('Disease')
    plt.ylabel('Accuracy')
    plt.xticks(range(len(DISEASES)), DISEASES, rotation=45)
    
    plt.tight_layout()
    plt.savefig('fast_training_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Training completed!")
    print(f"Best validation loss: {best_val_loss:.4f}")
    print(f"Final validation accuracy: {accuracy:.4f}")
    
    # Print per-disease results
    print("\nPer-Disease Accuracy:")
    for i, disease in enumerate(DISEASES):
        if len(disease_accuracies) > i:
            print(f"{disease}: {disease_accuracies[i]:.3f}")
    
    return model

def test_model():
    print("Testing the trained model...")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    model = FastMedicalClassifier(num_classes=len(DISEASES))
    
    try:
        model.load_state_dict(torch.load('best_fast_medical_model.pth', map_location=device))
        print("Loaded trained model successfully!")
    except:
        print("No trained model found. Training first...")
        model = train_model()
        if model is None:
            return
    
    model = model.to(device)
    model.eval()
    
    # Test with sample prediction
    sample_input = torch.randn(1, 3, 224, 224).to(device)
    
    with torch.no_grad():
        logits = model(sample_input)
        prediction = torch.sigmoid(logits).cpu().numpy()[0]
    
    print("\nSample Prediction Results:")
    print("-" * 60)
    for i, disease in enumerate(DISEASES):
        prob = prediction[i]
        positive = prob > 0.5
        uncertainty = abs(0.5 - prob)
        print(f"{disease}: Probability={prob:.3f}, Uncertainty={uncertainty:.3f}, Positive={positive}")
    
    return model

if __name__ == "__main__":
    # Train the model
    trained_model = train_model()
    
    if trained_model is not None:
        # Test the model
        test_model()
        
        print("\nFast Medical AI Model Training and Testing Complete!")
        print("Model saved as 'best_fast_medical_model.pth'")
        print("Results saved as 'fast_training_results.png'")
