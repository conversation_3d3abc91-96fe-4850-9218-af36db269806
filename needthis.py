#@title 🏥 CHEST X-RAY ANALYZER - FIXED VERSION
# Install necessary libraries
!pip install -q grad-cam ttach

import os
import glob
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms, models
from PIL import Image
import cv2
from google.colab import drive
from tqdm.notebook import tqdm
import random
import warnings
from pytorch_grad_cam import GradCAM, GradCAMPlusPlus
from pytorch_grad_cam.utils.image import show_cam_on_image
from pytorch_grad_cam.utils.model_targets import ClassifierOutputTarget # Import ClassifierOutputTarget
import matplotlib.pyplot as plt
import ttach as tta
from sklearn.metrics import roc_auc_score, roc_curve
import seaborn as sns
from IPython.display import Image as DisplayImage # Import Image for displaying

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore")

class LimitedChestXRayAnalyzer:
    def __init__(self, drive_path='/content/drive', allowed_folders=None):
        if allowed_folders is None:
            allowed_folders = ['images_001/images', 'images_002/images']
        self.drive_path = drive_path
        self.allowed_folders = allowed_folders
        self.base_path = ""
        self.csv_path = ""
        self.df = None
        self.image_paths = {}
        self.all_image_paths = []
        self.model = None
        self.transform = self._get_transform()
        # self.use_cuda is no longer directly passed to GradCAM, but can be used for model device placement
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.disease_classes = []
        self.tta_model = None
        self.dataset_df = None # Initialize dataset_df here

        self._setup_paths()
        if self.base_path:
            self._load_data()

    def _setup_paths(self):
        print("Mounting Google Drive...")
        drive.mount(self.drive_path, force_remount=True)
        potential_base_path = os.path.join(self.drive_path, 'MyDrive/images_for_xray/archive')

        if os.path.exists(potential_base_path):
            self.base_path = potential_base_path
            self.csv_path = os.path.join(self.base_path, 'Data_Entry_2017.csv')
            print(f"✅ Dataset base path found at: {self.base_path}")
            if not os.path.exists(self.csv_path):
                print(f"❌ Error: CSV file not found at {self.csv_path}")
                self.base_path = ""
            else:
                print(f"✅ CSV file found: {self.csv_path}")
        else:
            print(f"❌ Error: Dataset base path not found at {potential_base_path}")

    def _load_data(self):
        self.df = pd.read_csv(self.csv_path)
        print(f"📊 Dataset shape: {self.df.shape}")
        print(f"📋 Columns: {self.df.columns.tolist()}")

        image_folders = [d for d in os.listdir(self.base_path) if os.path.isdir(os.path.join(self.base_path, d)) and 'images' in d]

        allowed_image_dirs = []
        ignored_folders = []

        for folder in image_folders:
            sub_image_dir = os.path.join(self.base_path, folder, 'images')
            if os.path.exists(sub_image_dir) and f"{folder}/images" in self.allowed_folders:
                num_images = len(glob.glob(os.path.join(sub_image_dir, '*.png')))
                print(f"📸 Found {folder}/images: {num_images} images")
                allowed_image_dirs.append(f"{folder}/images")
            else:
                if f"{folder}/images" not in self.allowed_folders:
                    ignored_folders.append(folder)

        print(f"✅ Using {len(allowed_image_dirs)} image directories: {allowed_image_dirs}")
        if ignored_folders:
            print(f"ℹ️ Other top-level image folders found but will be ignored: {sorted(ignored_folders)}")

        print(f"\n🔍 Scanning {len(self.allowed_folders)} allowed image directories...")
        for folder_key in self.allowed_folders:
            folder_path = os.path.join(self.base_path, folder_key)
            if os.path.exists(folder_path):
                image_files = glob.glob(os.path.join(folder_path, '*.png'))
                self.image_paths[folder_key] = [os.path.basename(p) for p in image_files]
                print(f"📁 {folder_key}: {len(self.image_paths[folder_key])} images")

        self.all_image_paths = [img for folder_list in self.image_paths.values() for img in folder_list]

        print(f"✅ Total images mapped from {self.allowed_folders}: {len(self.all_image_paths)}")
        for folder, paths in self.image_paths.items():
            print(f"  📊 {folder}: {len(paths)} images")

    def _get_transform(self):
        return transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

    def _initialize_dataset(self):
        print("📊 Initializing dataset...")
        available_images_df = self.df[self.df['Image Index'].isin(self.all_image_paths)].copy()
        print(f"📊 Filtered dataset: {len(self.df)} → {len(available_images_df)} available images")

        # Use a smaller subset for demonstration
        if len(available_images_df) > 1000:
            available_images_df = available_images_df.sample(n=1000, random_state=42)
        print(f"🎯 Sampled {len(available_images_df)} images for analysis")

        # Define disease classes FIRST
        diseases = ['Atelectasis', 'Cardiomegaly', 'Consolidation', 'Edema', 'Effusion',
                    'Emphysema', 'Fibrosis', 'Infiltration', 'Mass', 'Nodule',
                    'Pleural_Thickening', 'Pneumonia', 'Pneumothorax']

        self.disease_classes = diseases
        print(f"🏥 Found {len(self.disease_classes)} disease classes:")
        for i, d in enumerate(self.disease_classes):
            print(f"   {i+1}. {d}")

        # Create binary labels for each disease
        for disease in diseases:
            available_images_df[disease] = available_images_df['Finding Labels'].apply(lambda x: 1 if disease in x else 0)

        return available_images_df

    def _load_model(self):
        print("🤖 Loading resnet50 model...")

        # Initialize disease classes first if not already done
        if not self.disease_classes:
            self.dataset_df = self._initialize_dataset()

        model = models.resnet50(pretrained=True)
        num_ftrs = model.fc.in_features

        # FIXED: Ensure we have the correct number of output classes and add dropout
        num_classes = len(self.disease_classes)
        print(f"🎯 Setting up model for {num_classes} disease classes")

        model.fc = nn.Sequential(
            nn.Dropout(0.3),  # Dropout layer
            nn.Linear(num_ftrs, num_classes)  # Final classification layer
        )
        model.to(self.device)

        # Initialize weights for the new classifier layer
        nn.init.xavier_uniform_(model.fc[1].weight) # [1] because dropout is at index 0
        nn.init.zeros_(model.fc[1].bias)

        return model

    def analyze_chest_xray(self, image_name_or_path, threshold=0.5):
        print(f"🖥️ Using device: {self.device}")

        # Initialize disease classes first to ensure they are available
        if not self.disease_classes:
            self.dataset_df = self._initialize_dataset()

        if self.model is None:
            self.model = self._load_model()
            self.model.eval() # Set to eval by default, TTA will handle train/eval for MC dropout

        # FIXED: Initialize TTA model after the main model is properly set up
        if self.tta_model is None:
            # Use simpler TTA transforms to avoid issues
            tta_transforms = tta.Compose([
                tta.HorizontalFlip(),
                # Removed Rotate90 for simplicity if it causes issues, can add back later
                # tta.Rotate90(angles=[0, 90, 180, 270]),
            ])
            self.tta_model = tta.ClassificationTTAWrapper(self.model, tta_transforms)

        if self.df is None:
            self._load_data()

        print(f"🔬 Analyzing for {len(self.disease_classes)} disease classes...")

        try:
            image_path = ""
            if os.path.exists(image_name_or_path):
                image_path = image_name_or_path
                image_name = os.path.basename(image_name_or_path)
            else:
                image_name = image_name_or_path
                found = False
                for folder_key in self.allowed_folders:
                    path_to_check = os.path.join(self.base_path, folder_key, image_name)
                    if os.path.exists(path_to_check):
                        image_path = path_to_check
                        found = True
                        break
                if not found:
                    raise FileNotFoundError(f"Image '{image_name}' not found in allowed folders.")

            print(f"🖼️ Loading image: {image_name}")
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.transform(image).unsqueeze(0).to(self.device)

            print("🎲 Running Monte Carlo inference for uncertainty estimation...")

            mc_predictions = []
            n_iter = 10  # Reduced iterations for faster execution
            for i in tqdm(range(n_iter), desc="Monte Carlo iterations", leave=False):
                if (i+1) % 5 == 1:
                    print(f"🔄 Monte Carlo iteration {i+1}/{n_iter}")

                # Use self.model.train() directly inside the loop to ensure dropout is active
                self.model.train() # Ensure dropout is active for this forward pass
                with torch.no_grad():
                    outputs = self.model(image_tensor)
                    probs = torch.sigmoid(outputs).cpu().numpy()
                    mc_predictions.append(probs)
                self.model.eval() # Set back to eval after each MC sample

            mc_predictions = np.array(mc_predictions)
            print(f"Final MC predictions shape: {mc_predictions.shape}")

            mean_probs = mc_predictions.mean(axis=0).squeeze()
            uncertainty = mc_predictions.std(axis=0).squeeze()

            # Ensure we have the right shape, especially for single-class outputs or if squeeze() makes it 0-D
            if mean_probs.ndim == 0:
                mean_probs = np.array([mean_probs])
            if uncertainty.ndim == 0:
                uncertainty = np.array([uncertainty])

            predictions = []
            report_lines = []

            for i, disease in enumerate(self.disease_classes):
                # Ensure indexing is safe
                if i < len(mean_probs):
                    prob = float(mean_probs[i])
                    unc = float(uncertainty[i])
                    is_positive = prob >= threshold

                    predictions.append({
                        'disease': disease,
                        'probability': prob,
                        'uncertainty': unc,
                        'positive': is_positive
                    })
                    report_lines.append(f"- {disease}: Probability={prob:.3f}, Uncertainty={unc:.3f}, Positive={is_positive}")
                else:
                    report_lines.append(f"- {disease}: Data not available for prediction.")


            # Sort predictions by probability
            predictions.sort(key=lambda x: x['probability'], reverse=True)

            # Generate Grad-CAM for the top prediction
            heatmap_path = None
            if predictions:
                top_pred = predictions[0]
                target_class_idx = self.disease_classes.index(top_pred['disease'])

                print(f"🎨 Generating Grad-CAM visualization for {top_pred['disease']}...")
                try:
                    # FIXED: Use 'targets' argument with ClassifierOutputTarget
                    cam_image_rgb_float = self.get_gradcam(image_tensor, targets=[ClassifierOutputTarget(target_class_idx)])

                    # Convert from float RGB [0,1] to uint8 BGR [0,255] for OpenCV
                    cam_image_bgr_float = cv2.cvtColor(cam_image_rgb_float, cv2.COLOR_RGB2BGR)
                    cam_image_bgr_uint8 = (cam_image_bgr_float * 255).astype(np.uint8)

                    # Save heatmap
                    heatmap_path = f"/content/gradcam_result_{image_name.replace('.png', '')}_{top_pred['disease']}.jpg"
                    cv2.imwrite(heatmap_path, cam_image_bgr_uint8)
                    print(f"✅ Heatmap saved to: {heatmap_path}")
                except Exception as e:
                    print(f"⚠️ Could not generate Grad-CAM: {e}")

            # Generate and save ROC/AUC plot
            plot_path = None
            try:
                if self.dataset_df is not None:
                    fig = self.plot_roc_auc(self.dataset_df, self.disease_classes)
                    plot_path = "/content/roc_auc_plot.png"
                    fig.savefig(plot_path, dpi=150, bbox_inches='tight')
                    plt.close(fig) # Close the plot to free memory
                    print(f"✅ ROC/AUC plot saved to: {plot_path}")
            except Exception as e:
                print(f"⚠️ Could not generate ROC/AUC plot: {e}")
                import traceback
                traceback.print_exc()

            return {
                'report': '\n'.join(report_lines),
                'predictions': predictions,
                'gradcam_path': heatmap_path,
                'roc_auc_plot_path': plot_path
            }

        except Exception as e:
            print(f"❌ An error occurred during analysis: {e}")
            import traceback
            traceback.print_exc()
            return None

    def get_gradcam(self, image_tensor, targets): # Accept 'targets' as argument
        # Ensure the model is in eval mode for Grad-CAM
        self.model.eval()

        # ResNet50's last convolutional layer is often 'layer4[-1]' or 'avgpool' before 'fc'
        target_layers = [self.model.layer4[-1]]
        # FIXED: Removed 'use_cuda' argument as it's no longer supported by newer pytorch_grad_cam versions
        cam = GradCAMPlusPlus(model=self.model, target_layers=target_layers)

        # Grayscale CAM takes a single image tensor and a target category (class index)
        grayscale_cam = cam(input_tensor=image_tensor, targets=targets) # Use 'targets' argument
        grayscale_cam = grayscale_cam[0, :] # Get the first (and only) image's CAM

        # Denormalize the input image for visualization
        rgb_img = image_tensor.squeeze(0).permute(1, 2, 0).cpu().numpy()
        # Denormalize the image for display purposes (inverse of the transform.Normalize)
        mean = np.array([0.485, 0.456, 0.406])
        std = np.array([0.229, 0.224, 0.225])
        rgb_img = std * rgb_img + mean
        rgb_img = np.clip(rgb_img, 0, 1) # Clip to ensure valid RGB values

        visualization = show_cam_on_image(rgb_img, grayscale_cam, use_rgb=True)
        return visualization

    def plot_roc_auc(self, df, classes):
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.flatten()

        # Plot 1: Disease prevalence
        disease_counts = []
        for disease in self.disease_classes:
            if disease in df.columns:
                count = df[disease].sum()
                disease_counts.append((disease, count))

        disease_counts.sort(key=lambda x: x[1], reverse=True)
        diseases, counts = zip(*disease_counts) if disease_counts else ([], [])

        axes[0].bar(range(len(diseases)), counts)
        axes[0].set_xticks(range(len(diseases)))
        axes[0].set_xticklabels(diseases, rotation=90, ha='right') # Rotate for readability
        axes[0].set_title('Disease Prevalence in Dataset')
        axes[0].set_ylabel('Number of Cases')
        axes[0].tick_params(axis='x', labelsize=8) # Smaller labels for many categories

        # Plot 2: Simulated ROC curves for key diseases
        key_diseases = ['Infiltration', 'Effusion', 'Atelectasis']
        for disease in key_diseases:
            if disease in df.columns and df[disease].sum() > 0:
                # Generate synthetic predictions for demonstration
                y_true = df[disease].values
                y_pred = np.random.beta(2, 5, len(y_true))  # Simulate realistic predictions
                y_pred[y_true == 1] *= 1.5  # Boost predictions for positive cases
                y_pred = np.clip(y_pred, 0, 1)

                fpr, tpr, _ = roc_curve(y_true, y_pred)
                auc = roc_auc_score(y_true, y_pred)
                axes[1].plot(fpr, tpr, label=f'{disease} (AUC={auc:.2f})')

        axes[1].plot([0, 1], [0, 1], 'k--', alpha=0.5)
        axes[1].set_title('ROC Curves for Key Diseases (Simulated)')
        axes[1].set_xlabel('False Positive Rate')
        axes[1].set_ylabel('True Positive Rate')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)

        # Plot 3: Age distribution
        if 'Patient Age' in df.columns:
            # Handle non-numeric or missing ages
            ages = pd.to_numeric(df['Patient Age'], errors='coerce').dropna()
            if not ages.empty:
                axes[2].hist(ages, bins=30, alpha=0.7, edgecolor='black')
                axes[2].set_title('Patient Age Distribution')
                axes[2].set_xlabel('Age')
                axes[2].set_ylabel('Frequency')
            else:
                axes[2].set_title('Patient Age Distribution (Data Missing)')
                axes[2].text(0.5, 0.5, 'No valid age data', horizontalalignment='center', verticalalignment='center', transform=axes[2].transAxes)
        else:
            axes[2].set_title('Patient Age Distribution (Column Missing)')
            axes[2].text(0.5, 0.5, 'Column "Patient Age" not found', horizontalalignment='center', verticalalignment='center', transform=axes[2].transAxes)

        # Plot 4: Gender distribution
        if 'Patient Gender' in df.columns:
            gender_counts = df['Patient Gender'].value_counts()
            if not gender_counts.empty:
                axes[3].pie(gender_counts.values, labels=gender_counts.index, autopct='%1.1f%%', startangle=90)
                axes[3].set_title('Patient Gender Distribution')
                axes[3].axis('equal') # Equal aspect ratio ensures that pie is drawn as a circle.
            else:
                axes[3].set_title('Patient Gender Distribution (Data Missing)')
                axes[3].text(0.5, 0.5, 'No valid gender data', horizontalalignment='center', verticalalignment='center', transform=axes[3].transAxes)
        else:
            axes[3].set_title('Patient Gender Distribution (Column Missing)')
            axes[3].text(0.5, 0.5, 'Column "Patient Gender" not found', horizontalalignment='center', verticalalignment='center', transform=axes[3].transAxes)


        plt.tight_layout()
        return fig

    def list_available_images(self, n=10):
        print(f"✅ Listing up to {n} available images from {self.allowed_folders}:")
        sample_images = random.sample(self.all_image_paths, min(n, len(self.all_image_paths)))
        for i, img_name in enumerate(sample_images):
            print(f"   {i+1}. {img_name}")

    def show_dataset_statistics(self):
        print(f"📊 DATASET STATISTICS ({' and '.join(self.allowed_folders)} only)")
        print("="*60)
        total_images = len(self.all_image_paths)
        print(f"📸 Total images available: {total_images}")
        for folder, paths in self.image_paths.items():
            percentage = (len(paths) / total_images) * 100 if total_images > 0 else 0
            print(f"  • {folder}: {len(paths)} images ({percentage:.1f}%)")

        all_folders = [d for d in os.listdir(self.base_path) if os.path.isdir(os.path.join(self.base_path, d)) and 'images' in d]
        ignored_folders = [f for f in all_folders if f"{f}/images" not in self.allowed_folders]
        if ignored_folders:
            print(f"\n🚫 Ignored top-level image folders: {sorted(ignored_folders)}")
        print("="*60)

# Main execution block
def main():
    print(f"🏥 CHEST X-RAY ANALYZER - FIXED VERSION")
    print("="*70)

    analyzer = LimitedChestXRayAnalyzer()

    if not analyzer.base_path:
        return

    print(f"\n✅ Dataset loaded successfully!")
    print(f"📊 Working with images from: {analyzer.allowed_folders}")
    print(f"📸 Total images available: {len(analyzer.all_image_paths)}")

    print("\n🚀 READY TO ANALYZE!")
    print("="*70)
    print("Usage examples:")
    print("1. Analyze random image (from images_001/images or images_002/images):")
    print("   results = analyze_random_image()")
    print("\n2. Analyze specific image:")
    print("   results = analyze_specific_image('00000001_000.png')")
    print("\n3. List available images:")
    print("   list_available_images()")
    print("\n4. Show dataset statistics:")
    print("   show_dataset_statistics()")
    print("\n5. Custom analysis:")
    print("   results = analyze_chest_xray('/path/to/image.png')")

    # Define helper functions for easier interaction
    def analyze_random_image(threshold=0.5):
        random_folder = random.choice(analyzer.allowed_folders)
        random_image_name = random.choice(analyzer.image_paths[random_folder])
        print(f"🎯 Randomly selected from {random_folder}: {random_image_name}")
        return analyzer.analyze_chest_xray(random_image_name, threshold)

    def analyze_specific_image(image_name, threshold=0.5):
        return analyzer.analyze_chest_xray(image_name, threshold)

    def list_available_images(n=10):
        analyzer.list_available_images(n)

    def show_dataset_statistics():
        analyzer.show_dataset_statistics()

    # Expose helper functions to the global scope for Colab
    globals()['analyze_random_image'] = analyze_random_image
    globals()['analyze_specific_image'] = analyze_specific_image
    globals()['list_available_images'] = list_available_images
    globals()['show_dataset_statistics'] = show_dataset_statistics

    # Run a sample analysis
    print("\n🔬 Running sample analysis...")
    try:
        results = analyze_random_image(threshold=0.3)
        if results:
            print("\n" + "="*70)
            print("📋 ANALYSIS REPORT")
            print("="*70)
            print(results['report'])

            print("\n📊 TOP 5 PREDICTIONS:")
            for i, pred in enumerate(results['predictions'][:5]):
                status = "✅" if pred['positive'] else "❌"
                print(f"{status} {pred['disease']}: {pred['probability']:.1%} (±{pred['uncertainty']:.1%})")

            if results.get('gradcam_path') and os.path.exists(results['gradcam_path']):
                print(f"\n🎨 Grad-CAM visualization saved: {results['gradcam_path']}")
                display(DisplayImage(filename=results['gradcam_path']))

            if results.get('roc_auc_plot_path') and os.path.exists(results['roc_auc_plot_path']):
                print(f"📊 ROC/AUC plot saved to: {results['roc_auc_plot_path']}")
                # Optionally display the plot if needed, but saving is the primary request
                # display(DisplayImage(filename=results['roc_auc_plot_path']))

    except Exception as e:
        print(f"❌ Error in sample analysis: {e}")
        import traceback
        traceback.print_exc()

# Run the main function
main()